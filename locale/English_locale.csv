ID|Text
CHAT_ALREADY_SCHEDULED|You've already scheduled a message to be delivered!
CHAT_CANCELLED|Message cancelled.
CHAT_MSG_FORM|Message from %1:<br/><br/>%2
CHAT_SENT|Message sent.
CHAT_TRY_AGAIN|Try again?
CHECK_CHAT_DATE|Your message will be delivered at\n%1.\nIs that correct?
CHOOSE_PASSWORD|%1, please choose a password:
CONFIRM_PASSWORD|Enter password again to confirm:
CONFIRM_SETTINGS|The settings for %1 have been changed.\n\nClick OK to start the game as %2.
CONTACT_MARKER|%1 at %2
CONTACTS_REPORTED|New contacts:
DETECTED_MARKER|%1 detected by %2
EIGHTH|EIGHTH
END_OF_SCENARIO_HEADER|End of Scenario
END_OF_SCENARIO_MESSAGE|Go to <b>File -> Save As...</b>, save this game, and send the save file to the other players via email or another transfer service.<br/><br/>
END_OF_SCENARIO_SUMMARY|End of Scenario (Turn %1)
END_OF_SETUP_HEADER|End of %1 Setup Phase
END_OF_TURN_HEADER|End of %1 Turn %2
END_OF_TURN_MESSAGE|Go to <b>File -> Save As...</b>, save this game, and send the save file to the <b>%1</b> player via email or another transfer service.<br/><br/><u>IMPORTANT:</u> Do <b>NOT</b> close this window or resume the game before saving, or you will have to replay your turn.
ENTER_CHAT|Enter message to %1 (max 280 characters):
ENTER_PASSWORD|%1, enter your password to start turn %2:
FIFTH|FIFTH
FINAL_ORDER_HEADER|%1 Turn %2<br/><br/>Order Phase %4
FINAL_ORDER_MESSAGE|Your turn is almost over. Give any last orders to your units as needed. When you're ready, <b>start the clock</b> to end your turn.
FIRST|FIRST
FORMAT_INCORRECT|Time formatting incorrect. Try again?
FOURTH|FOURTH
GAME_START|You are starting a PBEM game of %1.\n\nDo you want to use the recommended settings?
KILL_LISTING|with %1
KILL_MARKER|%1 killed
KILLS_REPORTED|Kills:
LOSS_LISTING|to %1
LOSS_MARKER|Loss of %1
LOSSES_REPORTED|Losses:
MESSAGES_RECEIVED|Messages received:
MIN_INCORRECT|The minute must be a number between 00 and 59. Try again?
NEXT_ORDER_HEADER|%1 Turn %2<br/>(%3 minutes left)<br/><br/>Order Phase %4
NINTH|NINTH
NO|No
NO_EDITOR_MODE|You can't open a PBEM game in Editor mode until the scenario has ended.\n\nPlay this scenario using Start New Game or Load Saved Game from the main menu.
NO_SIDE_FOUND|%1 isn't a side!
ORDER_PHASE_DIVIDER|%1 of %2
PASSWORDS_DIDNT_MATCH|Passwords didn't match! Please enter your password again:
RECOMMENDED|Recommended: %1
RESUME_ORDER_MESSAGE|You've already given orders for this phase. <b>Start the clock</b> to continue executing them.
SCHEDULE_CHAT|How long from now should the message be delivered?\nFORMAT: hh:mm:ss
SEC_INCORRECT|The second must be a number between 00 and 59. Try again?
SECOND|SECOND
SEND_CHAT|Send message to which side?\n\nOptions: %1
SETUP_PHASE_INTRO|This is the %1 setup phase.\n\nLeave the game paused until you've finished setting up your loadouts and missions.\n\nWhen you're done, click Play to end your turn.
SEVENTH|SEVENTH
SHOW_REMAINING_SETUP|This is the setup phase.
SHOW_REMAINING_TIME|Time remaining in your turn: %1:%2:%3.
SIXTH|SIXTH
SPEC_SCHEDMSG_DESC|Sends a message to another player, to be delivered at a time you schedule. Note that the maximum message length is 280 characters, HTML tags will be removed, and that you can only schedule one message for delivery at a time.
SPEC_SCHEDMSG_NAME|(PBEM) Schedule message for other player
SPEC_SENDMSG_DESC|Sends a message to another player, to be delivered at the start of their next turn. Note that the maximum message length is 280 characters, and that HTML tags will be removed.
SPEC_SENDMSG_NAME|(PBEM) Send message to other player
SPEC_SHOWTIME_DESC|Display the remaining time before your PBEM turn ends.
SPEC_SHOWTIME_NAME|(PBEM) Show remaining time in turn
START_OF_TURN_HEADER|%1 Turn %2<br/>(%3 minutes)
START_ORDER_HEADER|%1 Turn %2<br/>(%3 minutes)<br/><br/>Order Phase %4
START_ORDER_MESSAGE|Give orders to your units as needed. When you're ready, <b>start the clock</b> to execute them.
THIRD|THIRD
VERSION_MISMATCH|ERROR: You're using CMO build %1, but this PBEM game requires %2. Please update your game client.
VERSION_TOO_OLD|Your version of CMO (%1) is outdated. Please upgrade to build %2 or later to play this PBEM scenario.
VERSION_UPGRADE|(The current game has been automatically updated to CMO build %1.)
WIZARD_BACKUP|Please save a backup first, then RUN this tool again.
WIZARD_CLEAR_MISSIONS|Clear any existing missions for the %1 side?
WIZARD_GO_ORDER|Should the %1 side go %2?
WIZARD_INTRO_MESSAGE|Welcome to IKE v%1! This tool adds PBEM/hotseat play to any Command: Modern Operations scenario.\n\nRunning this tool cannot be undone. Have you saved a backup of this scenario?
WIZARD_ORDER_NUMBER|HOW MANY order phases will the %1 side have per turn? (Minimum: 2)
WIZARD_PLAYABLE_SIDE|Should the %1 side be PLAYABLE?
WIZARD_PREVENT_EDITOR|Do you want to PREVENT players from opening the game in EDITOR MODE until it's over?
WIZARD_SETUP_PHASE|Should the game start with a SETUP PHASE?
WIZARD_SUCCESS|Success! Your PBEM/hotseat scenario has been initialized. Go to FILE -> SAVE AS... to save it under a new name. It will be ready to play when next loaded.\n\n(If you're planning to publish it to the Steam Workshop, you should do it now, before you close this scenario.)\n\nThanks for using IKE!
WIZARD_TURN_LENGTH|Enter the TURN LENGTH in minutes:
WIZARD_ZERO_LENGTH|ERROR: The turn length must be greater than 0!
WRONG_PASSWORD|The password was incorrect.
YES|Yes
FRIENDLY|FRIENDLY
NEUTRAL|NEUTRAL
UNFRIENDLY|UNFRIENDLY
HOSTILE|HOSTILE
DRAW_NOSETUP|You can't offer a draw in the Setup Phase.
DRAW_WILLOFFER|Would you like to offer a draw to the other player?
DRAW_OFFERED|You have offered a draw. The other player will choose whether to accept on their next turn.
DRAW_ALREADY|You have already offered a draw this turn.
DRAW_ACCEPT|The other player has offered a draw. Do you accept? If YES, the match will end with no winner.
DRAW_ARESURE|Are you sure you want to accept a draw?
DRAW_MATCHOVER|Match ended in a draw.
RESIGN_ARESURE|Are you sure you want to resign?
RESIGN_RESIGNED|%1 resigned
SPEC_RESIGN_NAME|(PBEM) Resign the match
SPEC_RESIGN_DESC|Ends the match immediately in defeat for your side.
SPEC_DRAW_NAME|(PBEM) Offer a draw
SPEC_DRAW_DESC|Offers to end the match in a draw. If accepted by the other player, the match will end with no winner.
SCEN_END_DRAW|DRAW
SCEN_END_VICTORY|%1 VICTORY
SCEN_END_DEFEAT|%1 DEFEAT
DAMAGE_LISTING|hit by %1
DAMAGES_REPORTED|Damaged units:
UNKNOWN_WEAPON|unknown weapon
HIT_LISTING|hit with %1
HITS_REPORTED|Successful strikes:
SPEC_PASSCHANGE_NAME|(PBEM) Change your password
SPEC_PASSCHANGE_DESC|Changes your password. Useful if you want to show the turn to another player, but don't want to reveal your normal password.
WIZARD_CONST_TURN_LENGTHS|Use fixed turn lengths?\n\n(ADVANCED: Click No to allow players to alternate between Intermediate and Tactical turn lengths.)
WIZARD_INTERMEDIATE_LENGTH|Enter the INTERMEDIATE TURN LENGTH in minutes:
WIZARD_TACTICAL_LENGTH|Enter the TACTICAL TURN LENGTH in minutes:\n(Should be significantly shorter than Intermediate length)
VARIABLE_TIME_WARNING|This scenario uses variable turn lengths (Intermediate vs. Tactical). You can change the current turn length from your Special Actions menu.
SPEC_TACTICAL_NAME|(TIME) Begin Tactical time (%1 minute turns)
SPEC_TACTICAL_DESC|Switches the turn length to Tactical (%1 minutes), for more granular control over your units during intense action. This can only be done at the start of your turn.
SPEC_INTERMEDIATE_NAME|(TIME) Offer to enter Intermediate time (%1 minute turns)
SPEC_INTERMEDIATE_DESC|Offers to switch the turn length to Intermediate (%1 minutes), for faster game resolution during less intense parts of a scenario. If the other player agrees, Intermediate time will start on their turn.
TACTICAL_SETUP|You can't enter Tactical time during the Setup phase.
TACTICAL_NOTSTART|You can only enter Tactical time at the beginning of your turn.
TACTICAL_ENTERED|You have entered Tactical time. The length of all turns will now be %1 minutes.
TACTICAL_INITIATED|Another player has initiated Tactical time. The length of all turns will now be %1 minutes.
INTERMEDIATE_WILLOFFER|Would you like to offer to enter Intermediate time?
INTERMEDIATE_OFFERED|You have offered to enter Intermediate time. The other player will choose whether to accept on their next turn.
INTERMEDIATE_HASOFFER|Another player wants to enter Intermediate time. Do you accept? If YES, the length of all turns will now be %1 minutes.
INTERMEDIATE_ALREADY|An offer to enter Intermediate time has already been made.
INTERMEDIATE_VOTED|You have voted to enter Intermediate time. If the vote passes, Intermediate time will start on the turn of the last voting player.
INTERMEDIATE_DECLINED|You have declined the offer to enter Intermediate time.
INTERMEDIATE_ENTERED|The vote to enter Intermediate time has passed. The length of all turns will now be %1 minutes.
FRATRICIDES_REPORTED|Fratricides:
FATALERROR_CORRUPT|ERROR: Your PBEM turn has been corrupted. Do NOT save this file.\n\nPlease reload your turn, and play it again.
ID_GUIDEDWEAP|missile
ID_ROCKET|rocket
ID_BOMB|bomb
ID_GUNS|guns
ID_SUICIDEBOMB|suicide bomb
ID_SABOTAGEBOMB|explosives
ID_GUIDEDPROJ|smart round
ID_TORPEDO|torpedo
ID_DEPTHCHARGE|depth charge
ID_MINE|mine
ID_LASER|laser
SPEC_SHOWVERSION_NAME|(PBEM) About IKE
SPEC_SHOWVERSION_DESC|Displays information about IKE, the underlying code that powers this scenario's PBEM capabilities.
MSG_IKE_VERSION|IKE v%1\nLicensed under GNU GPL v3.\nhttps://github.com/musurca/IKE/
CURRENT_SETTING|Current setting: %1
SPEC_SETPREF_NAME|(PBEM) Change user preferences
SPEC_SETPREF_DESC|Customize your preferences for various aspects of PBEM play.
PREFERENCES_NOW_SET|Your preferences have been saved.
ASK_EVENT_MARK_RP|Create reference points to mark significant events that happened during the other player's turn?\n\n%1
ASK_EVENT_RP_DELETE_ENDTURN|At the end of your turn, clear reference points that marked significant events from the previous turn?\n\n%1
ASK_AUTOSAVE_END_TURN|At the end of your turn, autosave the game to \"Scenarios/PBEM OUT\"?\n\n%1
MARKPOINT_NAME|FROM %1: %2
NO_RPS_SELECTED|You must select at least one reference point to share.
SHARE_RP_NOT_ORDERPHASE|You can only share reference points during your Order Phase.
SHARE_RP_NO_SIDE_AVAIL|There are no allied players with whom you can share reference points.
SHARE_RP_WHICH_SIDE|Share reference points with which side?\n\nOptions: %1
SHARE_RP_CANCELLED|Sharing reference points has been cancelled.
SHARE_RP_SUCCESSFUL|%1 shared with %2.
SPEC_SHAREMP_NAME|(PBEM) Send reference point(s) to an allied player
SPEC_SHAREMP_DESC|Shares the selected reference point(s) with an ally.
AUTOSAVE_MESSAGE|<br/><br/>(For your convenience, your game has already been automatically saved to %1.)