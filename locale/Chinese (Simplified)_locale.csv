ID|Text
CHAT_ALREADY_SCHEDULED|您已经有一条待发送短消息！
CHAT_CANCELLED|短消息取消.
CHAT_MSG_FORM|短消息来自 %1:<br/><br/>%2
CHAT_SENT|短消息已发出.
CHAT_TRY_AGAIN|重试?
CHECK_CHAT_DATE|您的短消息将发送至\n%1.\n是否正确?
CHOOSE_PASSWORD|%1, 请输入密码:
CONFIRM_PASSWORD|请再次输入密码:
CONFIRM_SETTINGS|%1的设定已经更改.\n\n点击OK作为 %2 开始游戏.
CONTACT_MARKER|%1 在 %2
CONTACTS_REPORTED|新的信号接触:
DETECTED_MARKER|%1 被 %2 探测到
EIGHTH|第8顺位
END_OF_SCENARIO_HEADER|剧本结束
END_OF_SCENARIO_MESSAGE|选择 <b>文件 -> 另存为...</b>, 保存游戏, 并将保存文件发给另一方玩家.<br/><br/>
END_OF_SCENARIO_SUMMARY|剧本结束 (第 %1 回合)
END_OF_SETUP_HEADER| %1 的设置阶段结束
END_OF_TURN_HEADER|第 %1 回合结束 %2
END_OF_TURN_MESSAGE|打开<b>文件 -> 另存为...</b>, 保存当前剧本, 并将文件发送给玩家 <b>%1</b> .<br/><br/><u>重要提示:</u> 请 <b>不要</b> 在保存之前关闭窗口或运行游戏, 否则以前得重新来过.
ENTER_CHAT|向 %1 发送短消息 (最多 280 个字符):
ENTER_PASSWORD|%1, 请输入密码进行第 %2 回合:
FIFTH|第5顺位
FINAL_ORDER_HEADER|%1 回合 %2<br/><br/>指令阶段 %4
FINAL_ORDER_MESSAGE|你的回合快要结束了. 请向你的单位下达最后的命令. 当布置完成时,点击 <b>运行时间</b>结束当前回合.
FIRST|第1顺位
FORMAT_INCORRECT|时间格式错误. 请重试?
FOURTH|第4顺位
GAME_START|你现在将进行剧本 %1的对战.\n\n是否需要使用默认设置?
KILL_LISTING|为 %1
KILL_MARKER|%1 被击杀
KILLS_REPORTED|击杀:
LOSS_LISTING|达到 %1
LOSS_MARKER|%1的损失
LOSSES_REPORTED|损失:
MESSAGES_RECEIVED|您有一条新消息:
MIN_INCORRECT|分的数值应当在 00 到 59之前. 请重试?
NEXT_ORDER_HEADER|%1 回合 %2<br/>(剩余 %3 分钟 )<br/><br/>指令阶段 %4
NINTH|第9顺位
NO|否
NO_EDITOR_MODE|再游戏结束前你无法打开编辑器.\n\n请使用主菜单的 开始新游戏 或者 读取存档.
NO_SIDE_FOUND|%1 不是某个阵营!
ORDER_PHASE_DIVIDER|%2 分之 %1 
PASSWORDS_DIDNT_MATCH|密码不匹配! 请再次输入密码:
RECOMMENDED|推荐: %1
RESUME_ORDER_MESSAGE|当前阶段已经完成了下达指令. 点击<b>开始运行</b> 继续游戏.
SCHEDULE_CHAT|从现在开始计算多久之后发送消息？\n格式: hh:mm:ss
SEC_INCORRECT|秒的数值应当介于 00 到 59 之间. 请重试?
SECOND|第2顺位
SEND_CHAT|向哪一方发送消息?\n\n选项: %1
SETUP_PHASE_INTRO|现在是 %1 阵营的设置阶段.\n\n在完成设置挂载和任务之前请务必让游戏保持暂停状态.\n\n当完成布置,请点击Play结束此阶段.
SEVENTH|第7顺位
SHOW_REMAINING_SETUP|现在是布置阶段.
SHOW_REMAINING_TIME|回合剩余时间: %1:%2:%3.
SIXTH|第6顺位
SPEC_SCHEDMSG_DESC|向其他玩家发送短消息, 并按照预定时间送达. 请注意信息长度最多为280个字符且HTML标签将会被移除, 并且只能一次预定一条消息的发送时间.
SPEC_SCHEDMSG_NAME|(邮件对战) 预订向其他玩家发送短消息
SPEC_SENDMSG_DESC|向其他玩家发送的短消息将会在他们开启回合时显示. 请注意信息长度最多为280个字符且HTML标签将会被移除.
SPEC_SENDMSG_NAME|(邮件对战) 向其他玩家发送短消息
SPEC_SHOWTIME_DESC|在邮件对战的回合结束前,显示剩余时间.
SPEC_SHOWTIME_NAME|(邮件对战) 显示回合剩余时间
START_OF_TURN_HEADER|%1 回合 %2<br/>(%3 分钟)
START_ORDER_HEADER|%1 回合 %2<br/>(%3 分钟)<br/><br/>命令阶段 %4
START_ORDER_MESSAGE|请根据需求发布命令. 当命令设置完成后再点击<b>运行时间</b> .
THIRD|第3顺位
VERSION_MISMATCH|错误：您正在使用 CMO 版本 %1，但此 PBEM 游戏需要 %2。请更新您的游戏客户端。
VERSION_TOO_OLD|您的CMO当前版本 (%1)已过时. 请升级到 %2 版本后再进行PBEM对战.
VERSION_UPGRADE|(游戏已经自动升级至CMO 版本 %1.)
WIZARD_BACKUP|请先进行备份保存，然后运行此插件.
WIZARD_CLEAR_MISSIONS|是否清除 %1 阵营已有的任务?
WIZARD_GO_ORDER| %1 方将以 %2 进行游戏?
WIZARD_INTRO_MESSAGE|欢迎使用IKE v%1! 本插件能够应用于CMO任何剧本, 将其改为邮件对战或热座模式.\n\n运行插件之后源文件将被修改. 你是否已经对剧本进行了备份?
WIZARD_ORDER_NUMBER|%1 这一方玩家每回合最少能够进行几次操作? (最少2次)
WIZARD_PLAYABLE_SIDE|玩家能否扮演 %1 这一方吗?
WIZARD_PREVENT_EDITOR|你需要防止玩家在剧本完成之前使用编辑器模式吗?
WIZARD_SETUP_PHASE|游戏是否需要单独的设置阶段?
WIZARD_SUCCESS|成功! 你的邮件对战/热座对战剧本已经初始化完成. 选择文件 -> 另存为... 将其用新的文件名保存. 再下次读取时就能进行对战.\n\n(如果您计划将其发布在Steam创意工坊, 可以先不关闭此剧本并现在就上传.)\n\n感谢使用IKE!
WIZARD_TURN_LENGTH|输入回合长度（X分钟）：
WIZARD_ZERO_LENGTH|错误:回合长度必须大于0!
WRONG_PASSWORD|密码不正确.
YES|是
FRIENDLY|友好
NEUTRAL|中立
UNFRIENDLY|非友好
HOSTILE|敌对
DRAW_NOSETUP|可以在布置阶段提出求和.
DRAW_WILLOFFER|你是否想向其他玩家发出求和申请?
DRAW_OFFERED|你已经提出求和. 另一方玩家将在下回合决定是否接受.
DRAW_ALREADY|本回合已经发送求和申请.
DRAW_ACCEPT|另一方玩家提出求和. 是否接受? 如果是, 比赛将以平局结束.
DRAW_ARESURE|请确认是否接受平局?
DRAW_MATCHOVER|比赛以平局结束.
RESIGN_ARESURE|你确定要认输吗?
RESIGN_RESIGNED|%1 已经认输
SPEC_RESIGN_NAME|(PBEM) 认输
SPEC_RESIGN_DESC|立即认输并结束比赛.
SPEC_DRAW_NAME|(PBEM) 提出求和
SPEC_DRAW_DESC|提出以平局结束比赛. 如果其他玩家接受, 比赛将以平局结束.
SCEN_END_DRAW|平局
SCEN_END_VICTORY|%1 胜利
SCEN_END_DEFEAT|%1 失败
DAMAGE_LISTING|被 %1 击中
DAMAGES_REPORTED|损伤:
UNKNOWN_WEAPON|未知武器
HIT_LISTING|由 %1 击中
HITS_REPORTED|攻击成功:
SPEC_PASSCHANGE_NAME|(PBEM) 改变密码
SPEC_PASSCHANGE_DESC|如果想向他人展示对战，却不想泄露常用密码时，可以改变密码
WIZARD_CONST_TURN_LENGTHS|是否使用固定回合长度?\n\n(提示: 点击 No 会在 中继回合 和战术 回合间切换.)
WIZARD_INTERMEDIATE_LENGTH|输入中继回合长度，以分钟为单位:
WIZARD_TACTICAL_LENGTH|输入战术回合长度，以分钟为单位:\n(需要明显短于中继回合时间跨度)
VARIABLE_TIME_WARNING|此剧本将使用可变时长回合 (中继回合 和 战术回合). 可以在特殊行动菜单中更改时长.
SPEC_TACTICAL_NAME|(时间)初始战术回合时长 (每回合 %1 分钟)
SPEC_TACTICAL_DESC|切换回合长度至战术回合 (%1 分钟), 可以在激烈战斗中下达命令. 此设定只能在回合开始时执行.
SPEC_INTERMEDIATE_NAME|(时间)切换至中继回合时长 (每回合 %1 分钟)
SPEC_INTERMEDIATE_DESC|切换回合长度至中继回合 (%1 分钟), 适用于剧本中战斗不激烈的时段. 如果其他玩家同意, 中继回合时长将会在其回合生效.
TACTICAL_SETUP|在初始设置阶段无法进入战术回合.
TACTICAL_NOTSTART|只能在回合开始时进入战术回合时段.
TACTICAL_ENTERED|已经进入战术回合时段. 所有的回合时长将变为 %1 分钟.
TACTICAL_INITIATED|另一位玩家已开启了战术回合时段. 回合时长将变为 %1 分钟.
INTERMEDIATE_WILLOFFER|是否要提议进入中继回合时段?
INTERMEDIATE_OFFERED|已经提议进入中继回合时段. 另一为玩家将在下回合选择是否接受提议.
INTERMEDIATE_HASOFFER|另一为玩家想要进入中继回合时段. 是否同意? 如果同意, 所有的回合时长将变为 %1 分钟.
INTERMEDIATE_ALREADY|提议进入中继回合的消息已经发送.
INTERMEDIATE_VOTED|你已经提出进入中继回合. 如果提议通过, 中继回合将会从最后一名同意的玩家回合开始执行.
INTERMEDIATE_DECLINED|你已经拒绝进入中继回合的提议.
INTERMEDIATE_ENTERED|进入中继回合的提议获得通过. 所有回合的时长现在变为 %1 分钟.
FRATRICIDES_REPORTED|友军杀伤:
FATALERROR_CORRUPT|错误: 你的PBEM回合崩溃了.请 不要 保存此文件.\n\n请重新读取存档再运行一次.
ID_GUIDEDWEAP|导弹
ID_ROCKET|火箭
ID_BOMB|炸弹
ID_GUNS|火炮
ID_SUICIDEBOMB|自杀炸弹
ID_SABOTAGEBOMB|爆炸物
ID_GUIDEDPROJ|智能弹药
ID_TORPEDO|鱼雷
ID_DEPTHCHARGE|深水炸弹
ID_MINE|水雷
ID_LASER|激光
SPEC_SHOWVERSION_NAME|(邮件对战)关于IKE
SPEC_SHOWVERSION_DESC|显示IKE相关信息，IKE支持此想定功能的基础代码.
MSG_IKE_VERSION|IKE 版本%1\n 基于授权 GNU GPL v3.\nhttps://github.com/musurca/IKE/
CURRENT_SETTING|当前设置：%1
SPEC_SETPREF_NAME|(PBEM) 更改用户偏好
SPEC_SETPREF_DESC|自定义您对 PBEM 游戏各个方面的偏好.
PREFERENCES_NOW_SET|您的偏好已保存.
ASK_EVENT_MARK_RP|创建参考点以标记在其他玩家回合期间发生的重大事件？\n\n%1
ASK_EVENT_RP_DELETE_ENDTURN|在你的回合结束时，清除标记上一回合重要事件的参考点？\n\n%1
ASK_AUTOSAVE_END_TURN|在你的回合结束时，将游戏自动保存到“Scenarios/PBEM OUT”?\n\n%1
MARKPOINT_NAME|来自%1: %2
NO_RPS_SELECTED|您必须选择至少一个要共享的参考点.
SHARE_RP_NOT_ORDERPHASE|您只能在订单阶段共享参考点.
SHARE_RP_NO_SIDE_AVAIL|没有可以与您共享参考点的盟友玩家.
SHARE_RP_WHICH_SIDE|与哪一方共享参考点?\n\n选项: %1
SHARE_RP_CANCELLED|共享参考点已取消.
SHARE_RP_SUCCESSFUL|%1 与 %2 共享.
SPEC_SHAREMP_NAME|(PBEM) 向友方玩家发送参考点
SPEC_SHAREMP_DESC|与盟友共享选定的参考点.
AUTOSAVE_MESSAGE|<br/><br/>（为方便起见，您的游戏已自动保存到 %1。）