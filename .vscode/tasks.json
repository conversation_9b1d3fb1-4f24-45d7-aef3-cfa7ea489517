{
	// See https://go.microsoft.com/fwlink/?LinkId=733558
	// for the documentation about the tasks.json format
	"version": "2.0.0",
	"tasks": [
		{
			"label": "Build (Release)",
			"type": "shell",
			"command": "./build.sh",
			"problemMatcher": [],
			"group": {
				"kind": "build",
				"isDefault": true
			}
		},
		{
			"label": "Build (Debug)",
			"type": "shell",
			"command": "./build.sh debug",
			"problemMatcher": [],
			"group": {
				"kind": "build",
				"isDefault": false
			}
		}
	]
}