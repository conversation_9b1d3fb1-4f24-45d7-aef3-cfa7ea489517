# IKE Bug Fixes and Improvements TODO

## 1. Version Checking System Improvements

### 1.1 Support New CMO Version Formats
- [ ] Update `PBEM_GetBuildMajorMinor()` function to handle new version formats:
  - `"v1.07 - Build 1567.6"` → major=1567, minor=6
  - `"v1.08"` → map to build numbers for compatibility (e.g., 1608)
  - Maintain backward compatibility with `"1307.1"` format

### 1.2 Version Mismatch Warning System
- [ ] Implement `PBEM_ShowVersionWarningIfNeeded()` function
- [ ] Display warning when opening saves created with different CMO versions
- [ ] Show current CMO version vs. save file version
- [ ] Alert users about potential compatibility issues

### 1.3 Enhanced Version Comparison
- [ ] Improve version comparison logic for mixed format scenarios
- [ ] Handle edge cases where version formats differ between current and saved

## 2. Editor Mode Detection and Prevention

### 2.1 循环检测机制
- [ ] Implement `PBEM_CheckAndHandleEditorMode()` function
- [ ] Add loop detection for users in editor mode
- [ ] Track attempt count (`__EDITOR_MODE_ATTEMPTS`)
- [ ] Provide 3 attempts before forcing scenario closure

### 2.2 User Experience Improvements
- [ ] Display clear error messages with attempt counter
- [ ] Provide "Retry" and "Cancel" options
- [ ] Auto-reset attempt counter when not in editor mode
- [ ] Force scenario closure after 3 failed attempts

## 3. Strict Mode Implementation (NEW)

### 3.1 Strict Mode Toggle
- [ ] Create strict mode configuration option
- [ ] Add `__SCEN_STRICT_MODE` boolean flag
- [ ] Implement strict mode initialization in wizard

### 3.2 Developer Status Detection
- [ ] Implement loop detection for developer/debug mode
- [ ] Check for developer console access
- [ ] Detect debug/cheat mode activation
- [ ] Monitor for unauthorized scenario modifications

### 3.3 Strict Mode Enforcement
- [ ] Prevent scenario execution in developer mode when strict mode is enabled
- [ ] Display appropriate warnings for developer mode detection
- [ ] Implement graceful degradation or scenario termination
- [ ] Log strict mode violations for debugging

## 4. Code Quality and Maintenance

### 4.1 Editor.lua Improvements
- [ ] Review and validate the `pcall(Trigger_Delete, trig_name)` approach
- [ ] Ensure consistency with `Event_Create` deletion logic
- [ ] Optimize performance of create/delete operations

### 4.2 Error Handling
- [ ] Improve error messages for version mismatches
- [ ] Add better logging for debugging purposes
- [ ] Implement fallback mechanisms for edge cases

### 4.3 Testing and Validation
- [ ] Test with various CMO version formats
- [ ] Validate editor mode detection across different scenarios
- [ ] Test strict mode functionality
- [ ] Ensure backward compatibility with existing saves

## Implementation Priority

1. **High Priority**: Version checking improvements (1.1, 1.2)
2. **High Priority**: Editor mode detection (2.1, 2.2)
3. **Medium Priority**: Strict mode implementation (3.1, 3.2, 3.3)
4. **Low Priority**: Code quality improvements (4.1, 4.2, 4.3)

## Notes

- All changes should maintain backward compatibility with existing PBEM scenarios
- Consider performance impact of loop detection mechanisms
- Ensure proper localization support for new error messages
- Test thoroughly with different CMO versions and scenarios

## Files to Modify

- `src/pbem_checks.lua` - Main version and mode checking logic
- `src/pbem_main.lua` - Integration of new checking mechanisms
- `src/pbem_wizard.lua` - Strict mode configuration options
- `locale/*.csv` - New localization strings for error messages
